# 课消看板多值统计卡片设计方案

## 设计概述

根据用户需求，删除了extra-info显示方式，改为在平均周课消卡片中直接显示多个相关数据，采用网格布局的多值显示模式。

## 设计方案

### 1. StatisticsCard组件增强

#### 新增功能
- **多值显示模式**：支持在一个卡片中显示多个相关数据
- **网格布局**：自适应网格布局，根据数据数量自动调整列数
- **保持兼容性**：保留原有单值显示模式

#### 组件属性
```javascript
props: {
  // 原有属性
  title: String,
  value: [Number, String], // 单值模式使用
  unit: String,
  icon: [String, Object],
  iconColor: String,
  
  // 新增属性
  multiValues: Array // 多值模式使用
}
```

#### multiValues数据格式
```javascript
multiValues: [
  { label: '组周课消', value: 120.5, unit: '课时' },
  { label: '师均', value: 30.2, unit: '课时' },
  { label: '生均', value: 8.5, unit: '课时' }
]
```

### 2. 视觉设计

#### 布局结构
```
┌─────────────────────────────────┐
│ 📊 平均周课消                    │
├─────────────────────────────────┤
│ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ │
│ │组周 │ │师均 │ │生均 │ │     │ │
│ │课消 │ │     │ │     │ │     │ │
│ │120.5│ │30.2 │ │8.5  │ │     │ │
│ │课时 │ │课时 │ │课时 │ │     │ │
│ └─────┘ └─────┘ └─────┘ └─────┘ │
└─────────────────────────────────┘
```

#### CSS样式设计
```css
.multi-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
  margin-bottom: 8px;
}

.value-item {
  text-align: center;
}

.value-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  line-height: 1.2;
}

.value-number {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.value-unit {
  font-size: 10px;
  font-weight: 400;
  color: #909399;
  margin-left: 2px;
}
```

### 3. 数据展示策略

#### 管理课消看板
根据不同视图显示不同的数据组合：

**全局视图（查看所有组）**：
- 总周课消
- 组均
- 师均  
- 生均

**组视图（查看特定组）**：
- 组周课消
- 师均
- 生均

**老师视图（查看特定老师）**：
- 周课消

#### 组长课消看板
根据不同视图显示：

**组视图（查看组内所有老师）**：
- 组周课消
- 师均
- 生均

**老师视图（查看特定老师）**：
- 周课消

### 4. 技术实现

#### 组件模板
```html
<div class="card-content">
  <!-- 单值显示模式 -->
  <div v-if="!multiValues" class="main-value">
    {{ formatValue(value) }}
    <span v-if="unit" class="unit">{{ unit }}</span>
  </div>
  
  <!-- 多值显示模式 -->
  <div v-else class="multi-values">
    <div v-for="(item, index) in multiValues" :key="index" class="value-item">
      <div class="value-label">{{ item.label }}</div>
      <div class="value-number">
        {{ formatValue(item.value) }}
        <span v-if="item.unit" class="value-unit">{{ item.unit }}</span>
      </div>
    </div>
  </div>
</div>
```

#### 数据计算方法
```javascript
// 管理看板
getWeeklyConsumptionValues() {
  const stats = this.currentStats
  if (this.queryParams.teacherId) {
    return [
      { label: '周课消', value: stats?.avgWeeklyConsumption || 0, unit: '课时' }
    ]
  } else if (this.queryParams.groupId) {
    return [
      { label: '组周课消', value: stats?.avgWeeklyConsumption || 0, unit: '课时' },
      { label: '师均', value: stats?.avgWeeklyConsumptionPerTeacher || 0, unit: '课时' },
      { label: '生均', value: stats?.avgWeeklyConsumptionPerStudent || 0, unit: '课时' }
    ]
  }
  return [
    { label: '总周课消', value: stats?.avgWeeklyConsumption || 0, unit: '课时' },
    { label: '组均', value: stats?.avgWeeklyConsumptionPerGroup || 0, unit: '课时' },
    { label: '师均', value: stats?.avgWeeklyConsumptionPerTeacher || 0, unit: '课时' },
    { label: '生均', value: stats?.avgWeeklyConsumptionPerStudent || 0, unit: '课时' }
  ]
}

// 组长看板
getWeeklyConsumptionValues() {
  const stats = this.currentStats
  if (this.queryParams.teacherId) {
    return [
      { label: '周课消', value: stats?.avgWeeklyConsumption || 0, unit: '课时' }
    ]
  } else {
    return [
      { label: '组周课消', value: stats?.avgWeeklyConsumption || 0, unit: '课时' },
      { label: '师均', value: stats?.avgWeeklyConsumptionPerTeacher || 0, unit: '课时' },
      { label: '生均', value: stats?.avgWeeklyConsumptionPerStudent || 0, unit: '课时' }
    ]
  }
}
```

## 设计优势

### 1. 信息密度提升
- **空间利用**：在同样的卡片空间内展示更多相关数据
- **关联性强**：相关的课消数据集中展示，便于对比分析
- **视觉清晰**：网格布局整齐，数据层次分明

### 2. 用户体验优化
- **一目了然**：无需查看额外信息就能了解全貌
- **对比便捷**：师均、生均等数据并排显示，便于对比
- **响应式设计**：自适应不同屏幕尺寸

### 3. 数据分析价值
- **多维度分析**：同时展示总体、平均、细分数据
- **决策支持**：管理者能快速了解各层级的课消情况
- **趋势识别**：便于发现师均、生均之间的差异

## 视觉效果

### 卡片布局对比

#### 修改前（单值+extra-info）
```
┌─────────────────────────────────┐
│ 📊 平均周课消                    │
├─────────────────────────────────┤
│           120.5                 │
│          课时/周                │
│                                 │
│ 师均周课消: 30.2课时 | 生均周课消: 8.5课时 │
└─────────────────────────────────┘
```

#### 修改后（多值网格）
```
┌─────────────────────────────────┐
│ 📊 平均周课消                    │
├─────────────────────────────────┤
│ ┌─────┐ ┌─────┐ ┌─────┐         │
│ │组周 │ │师均 │ │生均 │         │
│ │课消 │ │     │ │     │         │
│ │120.5│ │30.2 │ │8.5  │         │
│ │课时 │ │课时 │ │课时 │         │
│ └─────┘ └─────┘ └─────┘         │
└─────────────────────────────────┘
```

### 响应式适配
- **4列布局**：全局视图（总周课消、组均、师均、生均）
- **3列布局**：组视图（组周课消、师均、生均）
- **1列布局**：老师视图（周课消）

## 技术特性

### 1. 兼容性保证
- **向后兼容**：保留原有单值显示模式
- **渐进增强**：新功能不影响现有功能
- **类型安全**：支持数字和字符串类型的值

### 2. 性能优化
- **按需渲染**：根据multiValues属性决定渲染模式
- **轻量级实现**：CSS Grid原生支持，无需额外依赖
- **缓存友好**：计算结果可缓存，避免重复计算

### 3. 可维护性
- **组件化设计**：功能封装在StatisticsCard组件中
- **数据驱动**：通过配置数据控制显示内容
- **易于扩展**：可轻松添加新的数据项

## 应用场景

### 1. 管理课消看板
- **全局监控**：查看整体课消情况和各维度平均值
- **组别对比**：对比不同组的师均、生均课消
- **深入分析**：从总体到组别到个人的层级分析

### 2. 组长课消看板  
- **组内管理**：了解组内整体和平均课消情况
- **老师评估**：对比不同老师的课消表现
- **资源配置**：基于师均、生均数据优化资源分配

## 总结

通过多值统计卡片设计，成功实现了：

**功能提升**：
- ✅ 信息密度更高，一个卡片展示多个相关数据
- ✅ 数据关联性更强，便于对比分析
- ✅ 用户体验更好，减少视线移动

**技术优势**：
- ✅ 组件化设计，易于维护和扩展
- ✅ 响应式布局，适配不同屏幕
- ✅ 向后兼容，不影响现有功能

**业务价值**：
- ✅ 提升决策效率，关键数据一目了然
- ✅ 增强分析能力，多维度数据并排展示
- ✅ 优化管理体验，减少操作步骤

这种设计既满足了用户的功能需求，又提升了整体的用户体验和数据分析价值。
