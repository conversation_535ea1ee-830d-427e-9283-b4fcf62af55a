-- 课消看板菜单配置和权限配置SQL
-- 执行时间：2025-01-27

-- =====================================================
-- 1. 菜单配置SQL
-- =====================================================

-- 插入课消看板父菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES (
    nextval('sys_menu_seq'), 
    '课消看板', 
    0, 
    4, 
    'course-consumption-dashboard', 
    NULL, 
    NULL, 
    1, 
    0, 
    'M', 
    '0', 
    '0', 
    NULL, 
    'monitor', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    '课程消费统计看板'
);

-- 获取刚插入的父菜单ID（用于后续子菜单）
-- 注意：实际执行时需要替换为真实的父菜单ID

-- 插入管理课消看板菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES (
    nextval('sys_menu_seq'), 
    '管理课消看板', 
    (SELECT menu_id FROM sys_menu WHERE menu_name = '课消看板' AND parent_id = 0), 
    1, 
    'admin', 
    'dashboard/course-consumption-role/admin/index', 
    NULL, 
    1, 
    0, 
    'C', 
    '0', 
    '0', 
    'dashboard:course-consumption:admin:view', 
    'chart', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    '管理员课消统计看板'
);

-- 插入老师课消看板菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES (
    nextval('sys_menu_seq'), 
    '老师课消看板', 
    (SELECT menu_id FROM sys_menu WHERE menu_name = '课消看板' AND parent_id = 0), 
    2, 
    'teacher', 
    'dashboard/course-consumption-role/teacher/index', 
    NULL, 
    1, 
    0, 
    'C', 
    '0', 
    '0', 
    'dashboard:course-consumption:teacher:view', 
    'user', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    '老师课消统计看板'
);

-- 插入组长课消看板菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES (
    nextval('sys_menu_seq'), 
    '组长课消看板', 
    (SELECT menu_id FROM sys_menu WHERE menu_name = '课消看板' AND parent_id = 0), 
    3, 
    'group-leader', 
    'dashboard/course-consumption-role/group-leader/index', 
    NULL, 
    1, 
    0, 
    'C', 
    '0', 
    '0', 
    'dashboard:course-consumption:group-leader:view', 
    'peoples', 
    'admin', 
    NOW(), 
    'admin', 
    NOW(), 
    '组长课消统计看板'
);

-- =====================================================
-- 2. 权限配置SQL
-- =====================================================

-- 为admin角色分配所有课消看板权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 
    (SELECT role_id FROM sys_role WHERE role_key = 'admin'), 
    menu_id 
FROM sys_menu 
WHERE menu_name IN ('课消看板', '管理课消看板', '老师课消看板', '组长课消看板');

-- 为hr角色分配所有课消看板权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 
    (SELECT role_id FROM sys_role WHERE role_key = 'hr'), 
    menu_id 
FROM sys_menu 
WHERE menu_name IN ('课消看板', '管理课消看板', '老师课消看板', '组长课消看板');

-- 为教学组长角色分配组长看板和老师看板权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 
    (SELECT role_id FROM sys_role WHERE role_key = 'teaching_group_leader'), 
    menu_id 
FROM sys_menu 
WHERE menu_name IN ('课消看板', '组长课消看板', '老师课消看板');

-- 为教务角色分配组长看板和老师看板权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 
    (SELECT role_id FROM sys_role WHERE role_key = 'academic_affairs'), 
    menu_id 
FROM sys_menu 
WHERE menu_name IN ('课消看板', '组长课消看板', '老师课消看板');

-- 为老师角色分配老师看板权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 
    (SELECT role_id FROM sys_role WHERE role_key = 'teacher'), 
    menu_id 
FROM sys_menu 
WHERE menu_name IN ('课消看板', '老师课消看板');

-- =====================================================
-- 3. 验证SQL（可选执行）
-- =====================================================

-- 验证菜单是否正确插入
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    p.menu_name as parent_name,
    m.path,
    m.component,
    m.perms,
    m.icon,
    m.order_num
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_name LIKE '%课消看板%'
ORDER BY m.parent_id, m.order_num;

-- 验证角色权限是否正确分配
SELECT 
    r.role_name,
    r.role_key,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_name LIKE '%课消看板%'
ORDER BY r.role_key, m.order_num;

-- =====================================================
-- 4. 回滚SQL（如需删除）
-- =====================================================

-- 删除角色菜单关联
-- DELETE FROM sys_role_menu WHERE menu_id IN (
--     SELECT menu_id FROM sys_menu WHERE menu_name LIKE '%课消看板%'
-- );

-- 删除菜单（注意：先删除子菜单，再删除父菜单）
-- DELETE FROM sys_menu WHERE menu_name IN ('管理课消看板', '老师课消看板', '组长课消看板');
-- DELETE FROM sys_menu WHERE menu_name = '课消看板' AND parent_id = 0;

-- =====================================================
-- 5. 说明文档
-- =====================================================

/*
菜单结构：
课消看板 (父菜单)
├── 管理课消看板 (admin, hr 可访问)
├── 老师课消看板 (admin, hr, teaching_group_leader, academic_affairs, teacher 可访问)
└── 组长课消看板 (admin, hr, teaching_group_leader, academic_affairs 可访问)

权限说明：
- admin: 所有看板权限
- hr: 所有看板权限  
- teaching_group_leader: 组长看板 + 老师看板
- academic_affairs: 组长看板 + 老师看板
- teacher: 仅老师看板

路由路径：
- /course-consumption-dashboard/admin
- /course-consumption-dashboard/teacher  
- /course-consumption-dashboard/group-leader

组件路径：
- dashboard/course-consumption-role/admin/index
- dashboard/course-consumption-role/teacher/index
- dashboard/course-consumption-role/group-leader/index

权限标识：
- dashboard:course-consumption:admin:view
- dashboard:course-consumption:teacher:view
- dashboard:course-consumption:group-leader:view
*/
