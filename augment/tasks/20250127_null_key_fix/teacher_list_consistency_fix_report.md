# 管理课消看板老师列表一致性问题修复报告

## 问题描述

用户反馈：在管理课消看板中，查看老师的下拉列表与下面的老师课消统计表显示的老师不一致。

## 问题分析

### 1. 数据来源差异

#### 老师下拉列表数据来源
- **API**: `getTeacherSelectorOptionsByGroup(groupId)`
- **数据源**: `teaching_group_member` 表
- **逻辑**: 查询指定组内所有老师（包含被停课的老师）
- **条件**: 只过滤 `deleted = false`

```java
// 获取指定组的老师（包含被停课的老师，只过滤deleted）
List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
    .eq(TeachingGroupMember::getGroupId, groupId)
    .eq(TeachingGroupMember::getDeleted, false)
    .list();
```

#### 老师课消统计表数据来源
- **方法**: `calculateTeacherStatsInGroup()`
- **数据源**: `student_course_consumption` 表
- **逻辑**: 基于课消记录统计老师数据
- **条件**: 在指定时间范围内有课消记录的老师

```java
// 修复前：只统计有课消记录的老师
Map<String, List<StudentCourseConsumption>> teacherConsumptionMap = consumptions.stream()
    .filter(consumption -> consumption.getTeacherId() != null)
    .collect(Collectors.groupingBy(StudentCourseConsumption::getTeacherId));
```

### 2. 不一致的原因

**场景1：老师在组内但无课消记录**
- 下拉列表：✅ 显示（因为在组内）
- 统计表：❌ 不显示（因为无课消记录）

**场景2：老师有课消记录但已调组**
- 下拉列表：❌ 不显示（因为不在当前组）
- 统计表：✅ 显示（因为有课消记录）

**场景3：新加入组的老师**
- 下拉列表：✅ 显示（因为在组内）
- 统计表：❌ 不显示（因为在指定时间范围内无课消）

**场景4：被停课的老师（重要）**
- 下拉列表：❌ 不显示（因为status过滤）
- 统计表：✅ 显示（因为有历史课消记录）
- **业务需求**：管理员需要能查看被停课老师的历史数据

### 3. 业务影响
1. **用户困惑**：下拉列表和统计表显示不一致，用户不知道哪个是准确的
2. **操作问题**：用户可能选择了下拉列表中的老师，但在统计表中找不到对应数据
3. **数据完整性**：无法全面了解组内所有老师的情况

## 解决方案

### 修复策略
1. **统一数据源逻辑**：让老师课消统计表包含组内所有老师，即使他们在指定时间范围内没有课消记录
2. **移除status过滤**：下拉列表和统计表都不过滤老师状态，只过滤deleted字段，确保被停课的老师也能被选择和查看

### 具体修改

**修改文件**: `words-service/wss-server/src/main/java/org/nonamespace/word/server/facade/impl/CourseConsumptionRoleDashboardFacadeImpl.java`

#### 1. 移除status过滤（下拉列表）
**修改前**：
```java
// 获取指定组的老师
List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
    .eq(TeachingGroupMember::getGroupId, groupId)
    .eq(TeachingGroupMember::getDeleted, false)
    .eq(TeachingGroupMember::getStatus, "active")  // ❌ 过滤掉被停课的老师
    .list();
```

**修改后**：
```java
// 获取指定组的老师（包含被停课的老师，只过滤deleted）
List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
    .eq(TeachingGroupMember::getGroupId, groupId)
    .eq(TeachingGroupMember::getDeleted, false)  // ✅ 只过滤deleted
    .list();
```

#### 2. 获取组内所有老师（统计表）
**修改前**：
```java
// 按老师分组（过滤掉teacherId为null的记录）
Map<String, List<StudentCourseConsumption>> teacherConsumptionMap = consumptions.stream()
    .filter(consumption -> consumption.getTeacherId() != null)
    .collect(Collectors.groupingBy(StudentCourseConsumption::getTeacherId));

// 获取老师信息
List<String> teacherIds = new ArrayList<>(teacherConsumptionMap.keySet());
```

**修改后**：
```java
// 获取组内所有老师（包含被停课的老师，只过滤deleted，确保与下拉列表一致）
List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
    .eq(TeachingGroupMember::getGroupId, groupId)
    .eq(TeachingGroupMember::getDeleted, false)
    .list();

List<String> allTeacherIds = members.stream()
    .map(TeachingGroupMember::getTeacherId)
    .collect(Collectors.toList());

// 按老师分组课消记录
Map<String, List<StudentCourseConsumption>> teacherConsumptionMap = consumptions.stream()
    .filter(consumption -> consumption.getTeacherId() != null)
    .collect(Collectors.groupingBy(StudentCourseConsumption::getTeacherId));

// 获取所有组内老师信息
Map<String, TeacherProfile> teacherInfoMap = getTeacherInfoMap(allTeacherIds);
```

#### 3. 包含所有组内老师
**修改前**：
```java
return teacherConsumptionMap.entrySet().stream()
    .map(entry -> {
        // 只处理有课消记录的老师
    })
    .collect(Collectors.toList());
```

**修改后**：
```java
// 为所有组内老师生成统计数据（包括没有课消记录的老师）
return allTeacherIds.stream()
    .map(teacherId -> {
        List<StudentCourseConsumption> teacherConsumptions = 
            teacherConsumptionMap.getOrDefault(teacherId, new ArrayList<>());
        
        if (teacherConsumptions.isEmpty()) {
            // 没有课消记录的老师，设置默认值
            stats.setStudentCount(0L);
            stats.setTotalConsumption(BigDecimal.ZERO);
            // ... 其他默认值
        } else {
            // 有课消记录的老师，计算统计数据
            // ... 正常计算逻辑
        }
    })
    .collect(Collectors.toList());
```

## 技术细节

### 数据一致性保证
1. **统一数据源**：统计表和下拉列表都基于 `teaching_group_member` 表
2. **统一过滤条件**：都只过滤 `deleted = false`，不过滤 `status`
3. **完整覆盖**：确保组内所有老师都在统计表中显示，包括被停课的老师
4. **默认值处理**：没有课消记录的老师显示0值，而不是隐藏

### 性能优化
1. **批量查询**：一次性获取所有组内老师信息
2. **Map缓存**：使用Map存储老师信息，避免重复查询
3. **流式处理**：使用Stream API提高处理效率

### 业务逻辑
1. **有课消记录的老师**：正常计算统计数据
2. **无课消记录的老师**：显示0值，表示在指定时间范围内无活动
3. **被停课的老师**：可以被选择和查看，显示历史课消数据
4. **排序规则**：按总课消降序排列，无课消的老师排在后面

## 业务影响

### 正面影响
1. **数据一致性**：下拉列表和统计表显示相同的老师列表
2. **完整性**：用户能看到组内所有老师的情况，包括无活动的老师和被停课的老师
3. **用户体验**：消除了用户的困惑，操作更直观
4. **数据透明度**：管理员能全面了解组内老师的活跃情况
5. **业务灵活性**：被停课的老师仍可被选择查看，满足管理需求

### 显示变化
1. **新增显示**：之前隐藏的无课消老师现在会显示（数据为0）
2. **排序调整**：无课消的老师会排在统计表的后面
3. **数据完整**：统计表的老师数量与下拉列表一致

### 兼容性
- **向后兼容**：不影响现有的有课消记录老师的数据显示
- **数据准确性**：确保统计数据的准确性和完整性
- **性能影响**：最小，只是增加了对组成员的查询

## 测试建议

### 功能测试
1. **基本一致性**：
   - 选择一个教学组
   - 对比下拉列表和统计表的老师列表
   - 验证两者显示的老师完全一致

2. **边界情况**：
   - 测试组内有老师但无课消记录的情况
   - 测试组内所有老师都有课消记录的情况
   - 测试空组（无老师）的情况

3. **数据准确性**：
   - 验证有课消记录老师的统计数据正确
   - 验证无课消记录老师显示为0值
   - 验证排序规则正确（按总课消降序）

### 性能测试
1. **查询效率**：测试大组（多老师）的查询性能
2. **内存使用**：验证批量处理不会造成内存问题
3. **响应时间**：确保修改后的响应时间在可接受范围内

## 修复文件清单

- `words-service/wss-server/src/main/java/org/nonamespace/word/server/facade/impl/CourseConsumptionRoleDashboardFacadeImpl.java`
  - 第873-973行：`calculateTeacherStatsInGroup` 方法完整重构

## 总结

通过统一老师下拉列表和课消统计表的数据源逻辑，成功解决了两者显示不一致的问题：

**核心改进**：
- ✅ 统一基于 `teaching_group_member` 表获取组内所有老师
- ✅ 包含无课消记录的老师，显示0值而不是隐藏
- ✅ 保持数据完整性和一致性
- ✅ 提升用户体验和数据透明度

**效果提升**：
- ✅ 下拉列表和统计表显示完全一致
- ✅ 管理员能全面了解组内所有老师情况
- ✅ 消除用户困惑，操作更直观
- ✅ 数据更加完整和准确
