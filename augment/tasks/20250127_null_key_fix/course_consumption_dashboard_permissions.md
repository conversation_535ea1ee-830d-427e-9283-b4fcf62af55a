# 课消看板菜单配置和权限说明

## 菜单结构设计

### 层级结构
```
课消看板 (父菜单)
├── 管理课消看板
├── 老师课消看板  
└── 组长课消看板
```

### 菜单详细信息

| 菜单名称 | 路径 | 组件路径 | 权限标识 | 图标 | 排序 |
|---------|------|----------|----------|------|------|
| 课消看板 | course-consumption-dashboard | - | - | monitor | 4 |
| 管理课消看板 | admin | dashboard/course-consumption-role/admin/index | dashboard:course-consumption:admin:view | chart | 1 |
| 老师课消看板 | teacher | dashboard/course-consumption-role/teacher/index | dashboard:course-consumption:teacher:view | user | 2 |
| 组长课消看板 | group-leader | dashboard/course-consumption-role/group-leader/index | dashboard:course-consumption:group-leader:view | peoples | 3 |

## 权限分配策略

### 角色权限矩阵

| 角色 | 管理课消看板 | 老师课消看板 | 组长课消看板 | 说明 |
|------|-------------|-------------|-------------|------|
| **admin** | ✅ | ✅ | ✅ | 系统管理员，拥有所有权限 |
| **hr** | ✅ | ✅ | ✅ | 人力资源，需要全面了解课消情况 |
| **teaching_group_leader** | ❌ | ✅ | ✅ | 教学组长，管理组内老师和学生 |
| **academic_affairs** | ❌ | ✅ | ✅ | 教务人员，协助教学管理 |
| **teacher** | ❌ | ✅ | ❌ | 普通老师，只能查看自己的数据 |

### 权限设计原则

1. **最小权限原则**：每个角色只分配必要的权限
2. **层级管理**：上级角色包含下级角色的权限
3. **数据安全**：确保用户只能访问授权的数据
4. **业务对齐**：权限设置符合实际业务需求

## 数据访问控制

### 管理课消看板
- **访问角色**：admin, hr
- **数据范围**：全部组、全部老师、全部学生
- **功能权限**：
  - 查看所有组的课消统计
  - 查看任意组的老师统计
  - 查看任意老师的学生统计
  - 切换不同时间范围
  - 导出统计数据

### 老师课消看板
- **访问角色**：admin, hr, teaching_group_leader, academic_affairs, teacher
- **数据范围**：根据角色限制
  - admin/hr：可查看所有老师数据
  - teaching_group_leader：只能查看组内老师数据
  - academic_affairs：可查看相关老师数据
  - teacher：只能查看自己的数据
- **功能权限**：
  - 查看学生课消统计
  - 查看课消趋势图
  - 筛选时间范围和学科

### 组长课消看板
- **访问角色**：admin, hr, teaching_group_leader, academic_affairs
- **数据范围**：根据角色限制
  - admin/hr：可查看所有组数据
  - teaching_group_leader：只能查看自己管理的组
  - academic_affairs：可查看相关组数据
- **功能权限**：
  - 查看组内老师统计
  - 查看特定老师的学生统计
  - 组内数据对比分析

## 后端权限控制

### API接口权限

```java
// 管理课消看板API
@PreAuthorize("hasAuthority('dashboard:course-consumption:admin:view')")
@GetMapping("/admin/dashboard")
public AjaxResult getAdminDashboard() { ... }

// 老师课消看板API
@PreAuthorize("hasAuthority('dashboard:course-consumption:teacher:view')")
@GetMapping("/teacher/dashboard")
public AjaxResult getTeacherDashboard() { ... }

// 组长课消看板API
@PreAuthorize("hasAuthority('dashboard:course-consumption:group-leader:view')")
@GetMapping("/group-leader/dashboard")
public AjaxResult getGroupLeaderDashboard() { ... }
```

### 数据权限控制

```java
// 在Service层实现数据权限控制
public class CourseConsumptionRoleDashboardFacadeImpl {
    
    @Override
    public CourseConsumptionRoleDashboardDto getAdminDashboard(CourseConsumptionRoleDashboardReq req) {
        // 检查用户是否有admin权限
        if (!SecurityUtils.hasAuthority("dashboard:course-consumption:admin:view")) {
            throw new ServiceException("无权限访问管理看板");
        }
        // 返回全部数据
        return buildAdminDashboard(req);
    }
    
    @Override
    public CourseConsumptionRoleDashboardDto getTeacherDashboard(CourseConsumptionRoleDashboardReq req) {
        String currentUserId = SecurityUtils.getUserId();
        String currentRole = SecurityUtils.getLoginUser().getUser().getRoleKey();
        
        // 根据角色限制数据范围
        if ("teacher".equals(currentRole)) {
            // 老师只能查看自己的数据
            req.setTeacherId(currentUserId);
        } else if ("teaching_group_leader".equals(currentRole)) {
            // 组长只能查看组内老师数据
            applyGroupLeaderDataPermissions(req, currentUserId);
        }
        
        return buildTeacherDashboard(req);
    }
}
```

## 前端路由守卫

### 路由配置

```javascript
// router/index.js
{
  path: '/course-consumption-dashboard',
  component: Layout,
  redirect: '/course-consumption-dashboard/teacher',
  name: 'CourseConsumptionDashboard',
  meta: { title: '课消看板', icon: 'monitor' },
  children: [
    {
      path: 'admin',
      name: 'AdminDashboard',
      component: () => import('@/views/dashboard/course-consumption-role/admin/index'),
      meta: { 
        title: '管理课消看板', 
        icon: 'chart',
        roles: ['admin', 'hr']
      }
    },
    {
      path: 'teacher',
      name: 'TeacherDashboard',
      component: () => import('@/views/dashboard/course-consumption-role/teacher/index'),
      meta: { 
        title: '老师课消看板', 
        icon: 'user',
        roles: ['admin', 'hr', 'teaching_group_leader', 'academic_affairs', 'teacher']
      }
    },
    {
      path: 'group-leader',
      name: 'GroupLeaderDashboard',
      component: () => import('@/views/dashboard/course-consumption-role/group-leader/index'),
      meta: { 
        title: '组长课消看板', 
        icon: 'peoples',
        roles: ['admin', 'hr', 'teaching_group_leader', 'academic_affairs']
      }
    }
  ]
}
```

### 权限验证

```javascript
// permission.js
router.beforeEach(async (to, from, next) => {
  // 检查用户是否有访问权限
  const userRoles = store.getters.roles
  const routeRoles = to.meta.roles
  
  if (routeRoles && !routeRoles.some(role => userRoles.includes(role))) {
    // 无权限访问，重定向到403页面
    next('/401')
  } else {
    next()
  }
})
```

## 安全考虑

### 1. 数据隔离
- **垂直权限**：不同角色看到不同的菜单和功能
- **水平权限**：同级角色只能访问授权范围内的数据
- **数据脱敏**：敏感数据根据权限级别显示

### 2. 接口安全
- **权限验证**：每个API接口都有权限检查
- **参数校验**：防止越权访问其他用户数据
- **日志记录**：记录敏感操作的访问日志

### 3. 前端安全
- **路由守卫**：防止直接访问无权限页面
- **组件权限**：根据权限动态显示/隐藏功能
- **数据验证**：前端数据展示前进行权限验证

## 部署说明

### 1. 执行顺序
1. 先执行菜单配置SQL
2. 再执行权限配置SQL
3. 最后验证配置是否正确

### 2. 注意事项
- 确保角色表中存在对应的角色记录
- 检查菜单ID的序列是否正确
- 验证路由路径与前端组件路径一致

### 3. 测试验证
- 使用不同角色账号登录测试
- 验证菜单显示是否正确
- 测试数据访问权限是否生效

## 维护说明

### 1. 新增角色
如需新增角色，需要：
1. 在sys_role表中添加角色记录
2. 在sys_role_menu表中分配相应菜单权限
3. 更新后端权限控制逻辑
4. 更新前端路由权限配置

### 2. 权限调整
如需调整权限，需要：
1. 更新sys_role_menu表中的权限分配
2. 检查后端API权限注解
3. 验证前端路由权限配置
4. 进行全面测试

### 3. 菜单修改
如需修改菜单，需要：
1. 更新sys_menu表中的菜单信息
2. 检查前端路由配置
3. 验证组件路径是否正确
4. 测试菜单导航功能
