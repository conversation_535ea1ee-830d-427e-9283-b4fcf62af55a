<template>
  <div class="teacher-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>老师课消看板</h2>
      <p class="page-description">查看我的学生课程消费统计数据</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <TimeRangeSelector 
          v-model="timeRange" 
          @change="handleTimeRangeChange"
        />
        
        <el-form-item label="学科">
          <el-select 
            v-model="queryParams.subject" 
            placeholder="选择学科"
            clearable
            style="width: 120px;"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <StatisticsCard
            title="总学生数"
            :value="dashboardData?.overallStats?.totalStudents || 0"
            unit="人"
            :icon="User"
            icon-color="#409EFF"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="总课消课时"
            :value="dashboardData?.overallStats?.totalConsumption || 0"
            unit="课时"
            :icon="Clock"
            icon-color="#67C23A"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="平均周课消"
            :value="dashboardData?.overallStats?.avgWeeklyConsumption || 0"
            unit="课时/周"
            :icon="TrendCharts"
            icon-color="#E6A23C"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="平均月课消"
            :value="dashboardData?.overallStats?.avgMonthlyConsumption || 0"
            unit="课时/月"
            :icon="DataAnalysis"
            icon-color="#F56C6C"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 课消趋势图 -->
    <div class="chart-section">
      <ConsumptionTrendChart
        title="周课消趋势"
        :data="dashboardData?.weeklyTrends || []"
        :loading="loading"
        height="400px"
      />
    </div>

    <!-- 学生课消详情表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">学生课消详情</span>
        </div>
      </template>
      
      <el-table
        :data="dashboardData?.studentStats || []"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'totalConsumption', order: 'descending' }"
      >
        <el-table-column prop="studentName" label="学生姓名" min-width="120" fixed="left" />
        <el-table-column prop="studentPhone" label="手机号" min-width="130" />
        <el-table-column prop="totalConsumption" label="总课消(课时)" min-width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="weeklyConsumption" label="周课消(课时)" min-width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.weeklyConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="monthlyConsumption" label="月课消(课时)" min-width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.monthlyConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="consumptionCount" label="课消次数" width="100" sortable />
        <el-table-column prop="lastConsumptionTime" label="最后课消时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastConsumptionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="学科分布" min-width="200">
          <template #default="{ row }">
            <div class="subject-tags">
              <el-tag 
                v-for="subject in row.subjectConsumptions" 
                :key="subject.subject"
                size="small"
                class="subject-tag"
              >
                {{ subject.subject }}: {{ formatNumber(subject.consumption) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewStudentDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 学生详情对话框 -->
    <StudentDetailDialog
      v-model="studentDetailVisible"
      :student-data="selectedStudent"
      @close="studentDetailVisible = false"
    />
  </div>
</template>

<script>
import { getTeacherDashboardData } from '@/api/dashboard/course-consumption-role'
import { formatDateTime } from '@/utils/date'
import { User, Clock, TrendCharts, DataAnalysis, Search, Refresh, Download } from '@element-plus/icons-vue'

// 导入共享组件
import TimeRangeSelector from '../shared/TimeRangeSelector.vue'
import StatisticsCard from '../shared/StatisticsCard.vue'
import ConsumptionTrendChart from '../shared/ConsumptionTrendChart.vue'
import StudentDetailDialog from './components/StudentDetailDialog.vue'

export default {
  name: 'TeacherDashboard',
  components: {
    User, Clock, TrendCharts, DataAnalysis, Search, Refresh, Download,
    TimeRangeSelector,
    StatisticsCard,
    ConsumptionTrendChart,
    StudentDetailDialog
  },
  data() {
    return {
      // 加载状态
      loading: false,

      // 查询参数
      queryParams: {
        startDate: '',
        endDate: '',
        timeRangeType: 'lastEightWeeks',
        subject: '',
        teacherId: ''
      },

      // 时间范围
      timeRange: {
        timeRangeType: 'lastEightWeeks',
        startDate: '',
        endDate: ''
      },

      // 看板数据
      dashboardData: null,

      // 学生详情对话框
      studentDetailVisible: false,
      selectedStudent: null
    }
  },



  created() {
    // 不在这里调用getList()，等待TimeRangeSelector初始化完成后再调用
  },

  methods: {
    /** 获取看板数据 */
    async getList() {
      this.loading = true
      try {
        const response = await getTeacherDashboardData(this.queryParams)
        if (response.code === 200) {
          this.dashboardData = response.data
        } else {
          this.$modal.msgError(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取老师看板数据失败:', error)
        this.$modal.msgError('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    /** 时间范围变化处理 */
    handleTimeRangeChange(timeRange) {
      this.queryParams.startDate = timeRange.startDate
      this.queryParams.endDate = timeRange.endDate
      this.queryParams.timeRangeType = timeRange.timeRangeType
      this.getList()
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.subject = ''
      this.timeRange = {
        timeRangeType: 'thisWeek',
        startDate: '',
        endDate: ''
      }
      this.handleQuery()
    },

    /** 查看学生详情 */
    viewStudentDetail(student) {
      this.selectedStudent = student
      this.studentDetailVisible = true
    },

    /** 导出数据 */
    exportData() {
      this.$modal.msgInfo('导出功能开发中...')
    },

    /** 格式化数字 */
    formatNumber(value) {
      if (value === null || value === undefined) return '0'
      const num = Number(value)
      if (isNaN(num)) return value
      return num % 1 === 0 ? num.toString() : num.toFixed(2)
    },

    /** 格式化日期时间 */
    formatDateTime(dateTime) {
      return formatDateTime(dateTime)
    }
  }
}
</script>

<style scoped>
.teacher-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-cards {
  margin-bottom: 20px;
}

.chart-section {
  margin-bottom: 20px;
}

.table-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.subject-tag {
  margin: 0;
}
</style>
