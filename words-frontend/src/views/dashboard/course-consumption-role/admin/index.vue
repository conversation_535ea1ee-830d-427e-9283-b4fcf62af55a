<template>
  <div class="admin-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>管理课消看板</h2>
      <p class="page-description">查看所有组的课程消费统计数据，支持切换查看组和老师详情</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <TimeRangeSelector 
          v-model="timeRange" 
          @change="handleTimeRangeChange"
        />
        
        <el-form-item label="查看组">
          <el-select 
            v-model="queryParams.groupId" 
            placeholder="选择组"
            clearable
            style="width: 150px;"
            @change="handleGroupChange"
          >
            <el-option 
              v-for="group in groupOptions" 
              :key="group.value"
              :label="group.label" 
              :value="group.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="查看老师" v-if="queryParams.groupId || queryParams.teacherId">
          <el-select
            v-model="queryParams.teacherId"
            placeholder="选择老师"
            clearable
            style="width: 150px;"
            @change="handleTeacherChange"
          >
            <el-option
              v-for="teacher in teacherOptions"
              :key="teacher.value"
              :label="teacher.label"
              :value="teacher.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="学科">
          <el-select 
            v-model="queryParams.subject" 
            placeholder="选择学科"
            clearable
            style="width: 120px;"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav" v-if="queryParams.groupId || queryParams.teacherId">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>
          <el-button type="text" @click="backToOverview">全部组</el-button>
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="queryParams.groupId">
          <el-button 
            type="text" 
            @click="backToGroupView"
            :disabled="!queryParams.teacherId"
          >
            {{ currentGroupName }}
          </el-button>
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="queryParams.teacherId">
          <span class="breadcrumb-text">{{ currentTeacherName }}</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <StatisticsCard
            :title="getStatsTitle('组数', '老师数', '老师数')"
            :value="getStatsValue('totalGroups', 'totalTeachers', 1)"
            unit="个"
            icon="Office"
            icon-color="#409EFF"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="学生数"
            :value="currentStats?.totalStudents || 0"
            unit="人"
            icon="UserFilled"
            icon-color="#67C23A"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="总课消课时"
            :value="currentStats?.totalConsumption || 0"
            unit="课时"
            icon="Clock"
            icon-color="#E6A23C"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="平均周课消"
            icon="TrendCharts"
            icon-color="#F56C6C"
            :multi-values="getWeeklyConsumptionValues()"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 课消趋势图 -->
    <div class="chart-section">
      <ConsumptionTrendChart
        :title="trendChartTitle"
        :data="dashboardData?.weeklyTrends || []"
        :loading="loading"
        height="400px"
      />
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ tableTitle }}</span>
        </div>
      </template>
      
      <!-- 组统计表格 -->
      <el-table
        v-if="currentViewType === 'groups'"
        :data="dashboardData?.groupStats || []"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'avgWeeklyConsumptionPerStudent', order: 'descending' }"
        @row-click="viewGroupDetail"
      >
        <el-table-column prop="groupName" label="组名称" min-width="150" fixed="left" />
        <el-table-column prop="leaderName" label="组长" min-width="120" />
        <el-table-column prop="teacherCount" label="老师数" width="100" sortable />
        <el-table-column prop="studentCount" label="学生数" width="100" sortable />
        <el-table-column prop="totalConsumption" label="总课消(课时)" min-width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="weeklyConsumption" label="周课消(课时)" min-width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.weeklyConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgWeeklyConsumptionPerTeacher" label="师均周课消(课时)" min-width="140" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.avgWeeklyConsumptionPerTeacher) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgWeeklyConsumptionPerStudent" label="生均周课消(课时)" min-width="140" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.avgWeeklyConsumptionPerStudent) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click.stop="viewGroupDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 老师统计表格 -->
      <el-table
        v-else-if="currentViewType === 'teachers'"
        :data="dashboardData?.teacherStats || []"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'avgWeeklyConsumptionPerStudent', order: 'descending' }"
        @row-click="viewTeacherDetail"
      >
        <el-table-column prop="teacherName" label="老师姓名" min-width="120" fixed="left" />
        <el-table-column prop="studentCount" label="学生数" width="100" sortable />
        <el-table-column prop="activeStudents" label="活跃学生" width="100" sortable />
        <el-table-column prop="totalConsumption" label="总课消(课时)" min-width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="weeklyConsumption" label="周课消(课时)" min-width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.weeklyConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgWeeklyConsumptionPerStudent" label="生均周课消(课时)" min-width="140" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.avgWeeklyConsumptionPerStudent) }}
          </template>
        </el-table-column>

        <el-table-column prop="lastConsumptionTime" label="最后课消时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastConsumptionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click.stop="viewTeacherDetail(row)"
            >
              查看学生
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 学生统计表格 -->
      <el-table 
        v-else-if="currentViewType === 'students'"
        :data="dashboardData?.studentStats || []" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'totalConsumption', order: 'descending' }"
      >
        <el-table-column prop="studentName" label="学生姓名" width="120" fixed="left" />
        <el-table-column prop="studentPhone" label="手机号" width="130" />
        <el-table-column prop="totalConsumption" label="总课消" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="weeklyConsumption" label="周课消" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.weeklyConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="monthlyConsumption" label="月课消" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.monthlyConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="consumptionCount" label="课消次数" width="100" sortable />
        <el-table-column prop="lastConsumptionTime" label="最后课消时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastConsumptionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="学科分布" min-width="200">
          <template #default="{ row }">
            <div class="subject-tags">
              <el-tag 
                v-for="subject in row.subjectConsumptions" 
                :key="subject.subject"
                size="small"
                class="subject-tag"
              >
                {{ subject.subject }}: {{ formatNumber(subject.consumption) }}课时
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewStudentDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 学生详情对话框 -->
    <StudentDetailDialog
      v-model="studentDetailVisible"
      :student-data="selectedStudent"
      @close="studentDetailVisible = false"
    />
  </div>
</template>

<script>
import {
  getAdminDashboardData,
  getGroupSelectorOptions,
  getTeacherSelectorOptionsByGroup
} from '@/api/dashboard/course-consumption-role'
import { formatDateTime } from '@/utils/date'

// 导入共享组件
import TimeRangeSelector from '../shared/TimeRangeSelector.vue'
import StatisticsCard from '../shared/StatisticsCard.vue'
import ConsumptionTrendChart from '../shared/ConsumptionTrendChart.vue'
import StudentDetailDialog from '../teacher/components/StudentDetailDialog.vue'

export default {
  name: 'AdminDashboard',
  components: {
    TimeRangeSelector,
    StatisticsCard,
    ConsumptionTrendChart,
    StudentDetailDialog
  },
  data() {
    return {
      // 加载状态
      loading: false,

      // 查询参数
      queryParams: {
        startDate: '',
        endDate: '',
        timeRangeType: 'lastEightWeeks',
        subject: '',
        groupId: '',
        teacherId: ''
      },

      // 时间范围
      timeRange: {
        timeRangeType: 'lastEightWeeks',
        startDate: '',
        endDate: ''
      },

      // 看板数据
      dashboardData: null,

      // 选择器选项
      groupOptions: [],
      teacherOptions: [],

      // 学生详情对话框
      studentDetailVisible: false,
      selectedStudent: null
    }
  },

  computed: {
    currentViewType() {
      if (this.queryParams.teacherId) return 'students'
      if (this.queryParams.groupId) return 'teachers'
      return 'groups'
    },
    
    currentStats() {
      if (this.queryParams.teacherId) {
        // 查看特定老师时，从学生数据计算统计
        const studentStats = this.dashboardData?.studentStats || []
        return {
          totalStudents: studentStats.length,
          activeStudents: studentStats.length,
          totalConsumption: studentStats.reduce((sum, student) => sum + (student.totalConsumption || 0), 0),
          avgWeeklyConsumption: studentStats.reduce((sum, student) => sum + (student.weeklyConsumption || 0), 0)
        }
      } else if (this.queryParams.groupId) {
        // 查看特定组时，从老师数据计算统计
        const teacherStats = this.dashboardData?.teacherStats || []
        const totalTeachers = teacherStats.length
        const totalStudents = teacherStats.reduce((sum, teacher) => sum + (teacher.studentCount || 0), 0)
        const totalConsumption = teacherStats.reduce((sum, teacher) => sum + (teacher.totalConsumption || 0), 0)
        const avgWeeklyConsumption = teacherStats.reduce((sum, teacher) => sum + (teacher.weeklyConsumption || 0), 0)

        return {
          totalGroups: 1,
          activeGroups: 1,
          totalTeachers: totalTeachers,
          activeTeachers: totalTeachers,
          totalStudents: totalStudents,
          activeStudents: teacherStats.reduce((sum, teacher) => sum + (teacher.activeStudents || 0), 0),
          totalConsumption: totalConsumption,
          avgWeeklyConsumption: avgWeeklyConsumption,
          // 计算师均周课消
          avgWeeklyConsumptionPerTeacher: totalTeachers > 0 ? (avgWeeklyConsumption / totalTeachers) : 0,
          // 计算生均周课消
          avgWeeklyConsumptionPerStudent: totalStudents > 0 ? (avgWeeklyConsumption / totalStudents) : 0
        }
      } else {
        return this.dashboardData?.overallStats
      }
    },
    
    currentGroupName() {
      const currentGroup = this.dashboardData?.currentGroup
      return currentGroup?.groupName || '未知组'
    },
    
    currentTeacherName() {
      const currentTeacher = this.dashboardData?.currentTeacher
      return currentTeacher?.teacherName || '未知老师'
    },
    
    trendChartTitle() {
      if (this.queryParams.teacherId) {
        return `${this.currentTeacherName}的周课消趋势`
      } else if (this.queryParams.groupId) {
        return `${this.currentGroupName}的周课消趋势`
      }
      return '全部组周课消趋势'
    },
    
    tableTitle() {
      if (this.queryParams.teacherId) {
        return `${this.currentTeacherName}的学生课消详情`
      } else if (this.queryParams.groupId) {
        return `${this.currentGroupName}的老师课消统计`
      }
      return '全部组课消统计'
    }
  },

  created() {
    this.loadGroupOptions()
    // 不在这里调用getList()，等待TimeRangeSelector初始化完成后再调用
  },

  methods: {
    /** 加载组选项 */
    async loadGroupOptions() {
      try {
        const response = await getGroupSelectorOptions()
        if (response.code === 200) {
          this.groupOptions = response.data.groups || []
        }
      } catch (error) {
        console.error('获取组选项失败:', error)
      }
    },

    /** 加载老师选项 */
    async loadTeacherOptions(groupId) {
      if (!groupId) {
        this.teacherOptions = []
        return
      }
      
      try {
        const response = await getTeacherSelectorOptionsByGroup(groupId)
        if (response.code === 200) {
          this.teacherOptions = response.data.teachers || []
        }
      } catch (error) {
        console.error('获取老师选项失败:', error)
        this.teacherOptions = []
      }
    },

    /** 获取看板数据 */
    async getList() {
      this.loading = true
      try {
        const response = await getAdminDashboardData(this.queryParams)
        if (response.code === 200) {
          this.dashboardData = response.data
        } else {
          this.$modal.msgError(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取管理看板数据失败:', error)
        this.$modal.msgError('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    /** 时间范围变化处理 */
    handleTimeRangeChange(timeRange) {
      this.queryParams.startDate = timeRange.startDate
      this.queryParams.endDate = timeRange.endDate
      this.queryParams.timeRangeType = timeRange.timeRangeType
      // TimeRangeSelector确保了日期有效性，直接调用API
      this.getList()
    },

    /** 组变化处理 */
    async handleGroupChange() {
      this.queryParams.teacherId = '' // 清空老师选择
      await this.loadTeacherOptions(this.queryParams.groupId)
      this.getList()
    },

    /** 老师变化处理 */
    handleTeacherChange() {
      this.getList()
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.subject = ''
      this.queryParams.groupId = ''
      this.queryParams.teacherId = ''
      this.teacherOptions = []
      this.timeRange = {
        timeRangeType: 'thisWeek',
        startDate: '',
        endDate: ''
      }
      this.handleQuery()
    },

    /** 返回总览 */
    backToOverview() {
      this.queryParams.groupId = ''
      this.queryParams.teacherId = ''
      this.teacherOptions = []
      this.getList()
    },

    /** 返回组视图 */
    backToGroupView() {
      this.queryParams.teacherId = ''
      this.getList()
    },

    /** 查看组详情 */
    viewGroupDetail(group) {
      console.log('查看组详情:', group)
      this.queryParams.groupId = group.groupId
      this.queryParams.teacherId = '' // 清除老师ID
      this.loadTeacherOptions(group.groupId)
      this.getList()
    },

    /** 查看老师详情 */
    async viewTeacherDetail(teacher) {
      this.queryParams.teacherId = teacher.teacherId

      // 如果当前有groupId，确保老师选项已加载
      if (this.queryParams.groupId) {
        // 检查当前老师是否在选项中
        const hasTeacherOption = this.teacherOptions.some(option => option.value === teacher.teacherId)
        if (!hasTeacherOption) {
          // 重新加载老师选项
          await this.loadTeacherOptions(this.queryParams.groupId)
        }
      }

      this.getList()
    },

    /** 查看学生详情 */
    viewStudentDetail(student) {
      this.selectedStudent = student
      this.studentDetailVisible = true
    },

    /** 导出数据 */
    exportData() {
      this.$modal.msgInfo('导出功能开发中...')
    },

    /** 获取统计标题 */
    getStatsTitle(groupTitle, teacherTitle, studentTitle) {
      if (this.queryParams.teacherId) return studentTitle
      if (this.queryParams.groupId) return teacherTitle
      return groupTitle
    },

    /** 获取统计值 */
    getStatsValue(groupField, teacherField, studentValue) {
      if (this.queryParams.teacherId) return studentValue
      return this.currentStats?.[this.queryParams.groupId ? teacherField : groupField] || 0
    },

    /** 获取统计额外信息 */
    getStatsExtra(groupField, teacherField, studentValue, groupLabel, teacherLabel, studentLabel) {
      const value = this.queryParams.teacherId ? studentValue : 
                   (this.currentStats?.[this.queryParams.groupId ? teacherField : groupField] || 0)
      const label = this.queryParams.teacherId ? studentLabel :
                   (this.queryParams.groupId ? teacherLabel : groupLabel)
      return [{ label, value, unit: '个' }]
    },

    /** 获取课消额外信息 */
    getConsumptionExtra() {
      if (this.queryParams.teacherId) {
        return [{ label: '该老师课消', value: this.currentStats?.totalConsumption || 0, unit: '课时' }]
      } else if (this.queryParams.groupId) {
        return [{ label: '该组课消', value: this.currentStats?.totalConsumption || 0, unit: '课时' }]
      }
      return [{ label: '全部课消', value: this.currentStats?.totalConsumption || 0, unit: '课时' }]
    },

    /** 获取周课消多值显示数据 */
    getWeeklyConsumptionValues() {
      const stats = this.currentStats
      if (this.queryParams.teacherId) {
        return [
          { label: '周课消', value: stats?.avgWeeklyConsumption || 0, unit: '' }
        ]
      } else if (this.queryParams.groupId) {
        return [
          { label: '组课消', value: stats?.avgWeeklyConsumption || 0, unit: '' },
          { label: '师均', value: stats?.avgWeeklyConsumptionPerTeacher || 0, unit: '' },
          { label: '生均', value: stats?.avgWeeklyConsumptionPerStudent || 0, unit: '' }
        ]
      }
      return [
        { label: '总课消', value: stats?.avgWeeklyConsumption || 0, unit: '' },
        { label: '组均', value: stats?.avgWeeklyConsumptionPerGroup || 0, unit: '' },
        { label: '师均', value: stats?.avgWeeklyConsumptionPerTeacher || 0, unit: '' },
        { label: '生均', value: stats?.avgWeeklyConsumptionPerStudent || 0, unit: '' }
      ]
    },

    /** 获取周课消额外信息 */
    getWeeklyExtra() {
      const stats = this.currentStats
      if (this.queryParams.teacherId) {
        return [{ label: '该老师周课消', value: stats?.avgWeeklyConsumption || 0, unit: '课时' }]
      } else if (this.queryParams.groupId) {
        return [
          { label: '师均周课消', value: stats?.avgWeeklyConsumptionPerTeacher || 0, unit: '课时' },
          { label: '生均周课消', value: stats?.avgWeeklyConsumptionPerStudent || 0, unit: '课时' }
        ]
      }
      return [
        { label: '组均周课消', value: stats?.avgWeeklyConsumptionPerGroup || 0, unit: '课时' },
        { label: '师均周课消', value: stats?.avgWeeklyConsumptionPerTeacher || 0, unit: '课时' }
      ]
    },

    /** 格式化数字 */
    formatNumber(value) {
      if (value === null || value === undefined) return '0'
      const num = Number(value)
      if (isNaN(num)) return value
      return num % 1 === 0 ? num.toString() : num.toFixed(2)
    },

    /** 格式化日期时间 */
    formatDateTime(dateTime) {
      return formatDateTime(dateTime)
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.breadcrumb-nav {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-cards {
  margin-bottom: 20px;
}

.chart-section {
  margin-bottom: 20px;
}

.table-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.subject-tag {
  margin: 0;
}

.el-table__row {
  cursor: pointer;
}

.el-table__row:hover {
  background-color: #f5f7fa;
}

.breadcrumb-text {
  display: inline-block;
  line-height: 1.5;
  vertical-align: middle;
}
</style>
