package org.nonamespace.word.rest.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.cache.SessionLevelCacheManager;
import org.nonamespace.word.server.util.CachePerformanceMonitor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存监控控制器
 * 用于监控SessionLevelCacheManager的性能和内存使用情况
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@Slf4j
@RestController
@RequestMapping("/monitor/cache")
@RequiredArgsConstructor
public class CacheMonitorController extends BaseController {

    private final CachePerformanceMonitor cachePerformanceMonitor;

    /**
     * 获取缓存性能统计
     */
    @GetMapping("/stats")
    public AjaxResult getCacheStats() {
        return handleOperation("获取缓存统计信息", () -> {
            Map<String, Object> stats = new HashMap<>();
            stats.put("cacheStatistics", cachePerformanceMonitor.getCacheStatistics());
            stats.put("threadContentionInfo", cachePerformanceMonitor.getThreadContentionInfo());
            stats.put("currentThreadCache", SessionLevelCacheManager.getCacheStats());
            stats.put("hasPerformanceIssues", cachePerformanceMonitor.hasPerformanceIssues());
            stats.put("performanceAdvice", cachePerformanceMonitor.getPerformanceAdvice());
            
            return stats;
        });
    }

    /**
     * 获取内存使用情况
     */
    @GetMapping("/memory")
    public AjaxResult getMemoryStats() {
        return handleOperation("获取内存使用情况", () -> {
            MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapMemory = memoryMXBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemory = memoryMXBean.getNonHeapMemoryUsage();
            
            Map<String, Object> memoryStats = new HashMap<>();
            
            // 堆内存信息
            Map<String, Object> heap = new HashMap<>();
            heap.put("used", formatBytes(heapMemory.getUsed()));
            heap.put("committed", formatBytes(heapMemory.getCommitted()));
            heap.put("max", formatBytes(heapMemory.getMax()));
            heap.put("usagePercentage", String.format("%.2f%%", 
                (double) heapMemory.getUsed() / heapMemory.getMax() * 100));
            memoryStats.put("heap", heap);
            
            // 非堆内存信息
            Map<String, Object> nonHeap = new HashMap<>();
            nonHeap.put("used", formatBytes(nonHeapMemory.getUsed()));
            nonHeap.put("committed", formatBytes(nonHeapMemory.getCommitted()));
            nonHeap.put("max", nonHeapMemory.getMax() > 0 ? formatBytes(nonHeapMemory.getMax()) : "无限制");
            memoryStats.put("nonHeap", nonHeap);
            
            return memoryStats;
        });
    }

    /**
     * 获取JVM线程信息
     */
    @GetMapping("/threads")
    public AjaxResult getThreadStats() {
        return handleOperation("获取线程统计信息", () -> {
            var threadMXBean = ManagementFactory.getThreadMXBean();
            
            Map<String, Object> threadStats = new HashMap<>();
            threadStats.put("threadCount", threadMXBean.getThreadCount());
            threadStats.put("peakThreadCount", threadMXBean.getPeakThreadCount());
            threadStats.put("daemonThreadCount", threadMXBean.getDaemonThreadCount());
            threadStats.put("totalStartedThreadCount", threadMXBean.getTotalStartedThreadCount());
            
            return threadStats;
        });
    }

    /**
     * 重置缓存统计信息
     */
    @PostMapping("/reset")
    public AjaxResult resetCacheStats() {
        return handleOperation("重置缓存统计信息", () -> {
            cachePerformanceMonitor.resetStatistics();
            return "缓存统计信息已重置";
        });
    }

    /**
     * 清除当前线程缓存
     */
    @PostMapping("/clear")
    public AjaxResult clearCurrentThreadCache() {
        return handleOperation("清除当前线程缓存", () -> {
            String beforeStats = SessionLevelCacheManager.getCacheStats();
            SessionLevelCacheManager.clearAllCaches();
            String afterStats = SessionLevelCacheManager.getCacheStats();
            
            Map<String, String> result = new HashMap<>();
            result.put("before", beforeStats);
            result.put("after", afterStats);
            result.put("message", "当前线程缓存已清除");
            
            return result;
        });
    }

    /**
     * 触发GC并获取内存变化
     */
    @PostMapping("/gc")
    public AjaxResult triggerGC() {
        return handleOperation("触发垃圾回收", () -> {
            MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage beforeHeap = memoryMXBean.getHeapMemoryUsage();
            
            // 触发GC
            System.gc();
            
            // 等待一下让GC完成
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            MemoryUsage afterHeap = memoryMXBean.getHeapMemoryUsage();
            
            Map<String, Object> gcResult = new HashMap<>();
            gcResult.put("beforeGC", formatBytes(beforeHeap.getUsed()));
            gcResult.put("afterGC", formatBytes(afterHeap.getUsed()));
            gcResult.put("freed", formatBytes(beforeHeap.getUsed() - afterHeap.getUsed()));
            gcResult.put("message", "GC已触发");
            
            return gcResult;
        });
    }

    /**
     * 获取完整的性能报告
     */
    @GetMapping("/report")
    public AjaxResult getPerformanceReport() {
        return handleOperation("获取性能报告", () -> {
            Map<String, Object> report = new HashMap<>();
            
            // 缓存统计
            report.put("cache", Map.of(
                "statistics", cachePerformanceMonitor.getCacheStatistics(),
                "threadContention", cachePerformanceMonitor.getThreadContentionInfo(),
                "currentThreadCache", SessionLevelCacheManager.getCacheStats(),
                "hasIssues", cachePerformanceMonitor.hasPerformanceIssues(),
                "advice", cachePerformanceMonitor.getPerformanceAdvice()
            ));
            
            // 内存统计
            MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
            MemoryUsage heapMemory = memoryMXBean.getHeapMemoryUsage();
            report.put("memory", Map.of(
                "heapUsed", formatBytes(heapMemory.getUsed()),
                "heapMax", formatBytes(heapMemory.getMax()),
                "heapUsagePercentage", String.format("%.2f%%", 
                    (double) heapMemory.getUsed() / heapMemory.getMax() * 100)
            ));
            
            // 线程统计
            var threadMXBean = ManagementFactory.getThreadMXBean();
            report.put("threads", Map.of(
                "count", threadMXBean.getThreadCount(),
                "peak", threadMXBean.getPeakThreadCount(),
                "daemon", threadMXBean.getDaemonThreadCount()
            ));
            
            return report;
        });
    }

    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
        return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
    }
}
