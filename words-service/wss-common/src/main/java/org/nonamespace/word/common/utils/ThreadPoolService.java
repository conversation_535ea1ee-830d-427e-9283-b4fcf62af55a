package org.nonamespace.word.common.utils;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.cache.SessionLevelCacheManager;
import org.nonamespace.word.common.config.ThreadPoolPropertiesConfig;
import org.nonamespace.word.common.misc.WssContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ThreadPoolService {

    @Autowired
    ThreadPoolPropertiesConfig threadPoolConfig;

    private static ExecutorService GLOBAL_EXECUTOR = null;

    private static ThreadPoolPropertiesConfig THREAD_CONFIG = null;

    @PostConstruct
    public void init() {
        log.info("线程池初始化中...");
        setThreadPoolConfig(threadPoolConfig);
        ThreadPoolService.doInitThreadPool();
        log.info("线程池初始化完成...");
    }

    private static void doInitThreadPool() {
        if (GLOBAL_EXECUTOR == null) {
            GLOBAL_EXECUTOR = new ThreadPoolExecutor(
                    THREAD_CONFIG.getCorePoolSize(),
                    THREAD_CONFIG.getMaximumPoolSize(),
                    THREAD_CONFIG.getKeepAliveTime(),
                    TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(3000),
                    new ThreadFactoryBuilder().setNamePrefix("words-pool-").build(),
                    // 当线程池无法接受新任务时，会抛出 RejectedExecutionException 异常，阻止任务的提交。
                    new ThreadPoolExecutor.AbortPolicy());
        }
    }

    public static void setThreadPoolConfig(ThreadPoolPropertiesConfig thisThreadPoolConfig) {
        ThreadPoolService.THREAD_CONFIG = thisThreadPoolConfig;
    }

    public static void submit(Runnable task) {
        String name = task.getClass().getName();
        log.info("收到线程池任务: {}", name);
        Runnable wrappedTask = () -> {
            try {
                task.run();
            } catch (Exception e) {
                log.error("线程池任务执行失败: {}", name);
                log.error("线程池任务执行异常: {}", e.getMessage(), e);
            } finally {
                log.info("线程池任务执行完成: {}", name);
                WssContext.release();
                SessionLevelCacheManager.clearAllCaches();
            }
        };
        GLOBAL_EXECUTOR.submit(wrappedTask);
    }
}
