package org.nonamespace.word.common.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 会话级别缓存管理器
 * 优化版本：使用ThreadLocal + HashMap避免ReentrantLock竞争
 * 保持request级别的缓存隔离功能
 *
 * <AUTHOR>
 * @date 2025-01-15
 * @version 2.0 - 性能优化版本
 */
@Slf4j
public class SessionLevelCacheManager implements CacheManager {

    /**
     * 线程本地缓存存储
     * 使用HashMap替代ConcurrentHashMap避免锁竞争
     * 因为ThreadLocal保证了线程安全，不需要ConcurrentHashMap的锁机制
     */
    private static final ThreadLocal<Map<String, Cache>> CACHE_HOLDER =
            ThreadLocal.withInitial(HashMap::new);

    /**
     * 全局缓存实例池，减少对象创建开销
     * Key: 缓存名称, Value: 缓存实例模板
     */
    private static final Map<String, RequestLevelCache> CACHE_TEMPLATES =
            new ConcurrentHashMap<>();

    /**
     * 预定义的缓存名称
     */
    private final Collection<String> cacheNames;

    public SessionLevelCacheManager(Collection<String> cacheNames) {
        this.cacheNames = cacheNames;
        // 预创建缓存模板，减少运行时开销
        for (String cacheName : cacheNames) {
            CACHE_TEMPLATES.put(cacheName, new RequestLevelCache(cacheName));
        }
    }

    @Override
    @Nullable
    public Cache getCache(@NonNull String name) {
        Map<String, Cache> caches = CACHE_HOLDER.get();

        // 如果缓存已存在，直接返回
        Cache cache = caches.get(name);
        if (cache != null) {
            return cache;
        }

        // 从模板创建新的缓存实例，避免重复创建
        RequestLevelCache template = CACHE_TEMPLATES.get(name);
        if (template != null) {
            cache = template.createRequestInstance();
        } else {
            // 动态缓存名称，创建新实例
            cache = new RequestLevelCache(name).createRequestInstance();
        }

        // 直接put，因为HashMap在ThreadLocal中是线程安全的
        caches.put(name, cache);

        log.debug("创建请求级别缓存: {}", name);
        return cache;
    }

    @Override
    @NonNull
    public Collection<String> getCacheNames() {
        return cacheNames;
    }

    /**
     * 清除当前线程的所有缓存
     */
    public static void clearAllCaches() {
        Map<String, Cache> caches = CACHE_HOLDER.get();
        if (caches != null && !caches.isEmpty()) {
            // 清除缓存内容但不调用clear()方法，避免不必要的操作
            caches.clear();
            log.debug("已清除当前线程的所有请求级别缓存，缓存数量: {}", caches.size());
        }
        // 移除ThreadLocal，防止内存泄漏
        CACHE_HOLDER.remove();
    }

    /**
     * 获取当前线程缓存统计信息（用于监控）
     */
    public static String getCacheStats() {
        Map<String, Cache> caches = CACHE_HOLDER.get();
        if (caches == null || caches.isEmpty()) {
            return "无缓存";
        }
        return String.format("缓存数量: %d, 缓存名称: %s",
                caches.size(), caches.keySet());
    }

    /**
     * 请求级别缓存实现
     * 使用简单的HashMap作为存储，避免ConcurrentHashMap的锁开销
     */
    private static class RequestLevelCache {
        private final String name;

        public RequestLevelCache(String name) {
            this.name = name;
        }

        /**
         * 为当前请求创建缓存实例
         */
        public Cache createRequestInstance() {
            return new SimpleRequestCache(name);
        }
    }

    /**
     * 简单的请求级别缓存实现
     * 使用HashMap存储，因为在ThreadLocal中不需要线程安全
     */
    private static class SimpleRequestCache implements Cache {
        private final String name;
        private final Map<Object, Object> store;

        public SimpleRequestCache(String name) {
            this.name = name;
            this.store = new HashMap<>();
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public Object getNativeCache() {
            return store;
        }

        @Override
        @Nullable
        public ValueWrapper get(@NonNull Object key) {
            Object value = store.get(key);
            return value != null ? new SimpleValueWrapper(value) : null;
        }

        @Override
        @Nullable
        public <T> T get(@NonNull Object key, @Nullable Class<T> type) {
            Object value = store.get(key);
            if (value != null && type != null && !type.isInstance(value)) {
                throw new IllegalStateException("Cached value is not of required type [" + type.getName() + "]: " + value);
            }
            return (T) value;
        }

        @Override
        @Nullable
        public <T> T get(@NonNull Object key, @NonNull Callable<T> valueLoader) {
            Object value = store.get(key);
            if (value != null) {
                return (T) value;
            }

            try {
                T loadedValue = valueLoader.call();
                if (loadedValue != null) {
                    store.put(key, loadedValue);
                }
                return loadedValue;
            } catch (Exception e) {
                throw new RuntimeException("Value loader threw exception", e);
            }
        }

        @Override
        public void put(@NonNull Object key, @Nullable Object value) {
            store.put(key, value);
        }

        @Override
        @Nullable
        public ValueWrapper putIfAbsent(@NonNull Object key, @Nullable Object value) {
            Object existing = store.putIfAbsent(key, value);
            return existing != null ? new SimpleValueWrapper(existing) : null;
        }

        @Override
        public void evict(@NonNull Object key) {
            store.remove(key);
        }

        @Override
        public void clear() {
            store.clear();
        }

        @Override
        public String toString() {
            return "SimpleRequestCache{name='" + name + "', size=" + store.size() + "}";
        }
    }

    /**
     * 简单的值包装器
     */
    private static class SimpleValueWrapper implements Cache.ValueWrapper {
        private final Object value;

        public SimpleValueWrapper(Object value) {
            this.value = value;
        }

        @Override
        @Nullable
        public Object get() {
            return value;
        }
    }
}
