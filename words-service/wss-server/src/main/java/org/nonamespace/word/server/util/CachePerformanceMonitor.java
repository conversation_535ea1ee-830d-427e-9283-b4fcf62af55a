package org.nonamespace.word.server.util;

import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.common.cache.SessionLevelCacheManager;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 缓存性能监控工具
 * 用于监控SessionLevelCacheManager的性能改进效果
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@Slf4j
@Component
public class CachePerformanceMonitor {

    private final AtomicLong cacheHitCount = new AtomicLong(0);
    private final AtomicLong cacheMissCount = new AtomicLong(0);
    private final AtomicLong cacheCreateCount = new AtomicLong(0);
    
    private final ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();

    /**
     * 记录缓存命中
     */
    public void recordCacheHit() {
        cacheHitCount.incrementAndGet();
    }

    /**
     * 记录缓存未命中
     */
    public void recordCacheMiss() {
        cacheMissCount.incrementAndGet();
    }

    /**
     * 记录缓存创建
     */
    public void recordCacheCreate() {
        cacheCreateCount.incrementAndGet();
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStatistics() {
        long hits = cacheHitCount.get();
        long misses = cacheMissCount.get();
        long creates = cacheCreateCount.get();
        long total = hits + misses;
        
        double hitRate = total > 0 ? (double) hits / total * 100 : 0;
        
        return String.format(
            "缓存统计 - 命中: %d, 未命中: %d, 创建: %d, 命中率: %.2f%%, 当前线程缓存: %s",
            hits, misses, creates, hitRate, SessionLevelCacheManager.getCacheStats()
        );
    }

    /**
     * 获取线程竞争信息
     */
    public String getThreadContentionInfo() {
        if (!threadMXBean.isThreadContentionMonitoringSupported()) {
            return "线程竞争监控不支持";
        }
        
        if (!threadMXBean.isThreadContentionMonitoringEnabled()) {
            threadMXBean.setThreadContentionMonitoringEnabled(true);
        }
        
        long[] threadIds = threadMXBean.getAllThreadIds();
        long totalBlockedTime = 0;
        long totalWaitedTime = 0;
        int blockedThreads = 0;
        
        for (long threadId : threadIds) {
            try {
                long blockedTime = threadMXBean.getThreadInfo(threadId).getBlockedTime();
                long waitedTime = threadMXBean.getThreadInfo(threadId).getWaitedTime();
                
                if (blockedTime > 0) {
                    totalBlockedTime += blockedTime;
                    blockedThreads++;
                }
                totalWaitedTime += waitedTime;
            } catch (Exception e) {
                // 忽略线程不存在的情况
            }
        }
        
        return String.format(
            "线程竞争 - 活跃线程: %d, 阻塞线程: %d, 总阻塞时间: %dms, 总等待时间: %dms",
            threadIds.length, blockedThreads, totalBlockedTime, totalWaitedTime
        );
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        cacheHitCount.set(0);
        cacheMissCount.set(0);
        cacheCreateCount.set(0);
        log.info("缓存性能统计信息已重置");
    }

    /**
     * 打印详细的性能报告
     */
    public void printPerformanceReport() {
        log.info("=== 缓存性能报告 ===");
        log.info(getCacheStatistics());
        log.info(getThreadContentionInfo());
        log.info("==================");
    }

    /**
     * 检查是否存在性能问题
     */
    public boolean hasPerformanceIssues() {
        long hits = cacheHitCount.get();
        long misses = cacheMissCount.get();
        long total = hits + misses;
        
        if (total == 0) {
            return false;
        }
        
        double hitRate = (double) hits / total * 100;
        
        // 如果命中率低于50%，认为存在性能问题
        return hitRate < 50.0;
    }

    /**
     * 获取性能建议
     */
    public String getPerformanceAdvice() {
        if (!hasPerformanceIssues()) {
            return "缓存性能良好";
        }
        
        long hits = cacheHitCount.get();
        long misses = cacheMissCount.get();
        long total = hits + misses;
        double hitRate = (double) hits / total * 100;
        
        StringBuilder advice = new StringBuilder();
        advice.append("缓存性能建议：\n");
        
        if (hitRate < 30) {
            advice.append("- 命中率过低(").append(String.format("%.2f", hitRate))
                  .append("%)，考虑调整缓存策略\n");
        }
        
        if (cacheCreateCount.get() > total * 0.8) {
            advice.append("- 缓存创建过于频繁，考虑增加缓存生命周期\n");
        }
        
        return advice.toString();
    }
}
